'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import Image from 'next/image';

export default function Footer() {
  const { t, language } = useLanguage();

  return (
    <footer className="relative py-12 glass-effect border-t border-white/10">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-3 gap-8 mb-8">
          {/* Logo and Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="space-y-4"
          >
            <div className="flex items-center space-x-3">
              <Image
                src="/LOGO.jpg"
                alt="Studio Aosumi Logo"
                width={40}
                height={40}
                className="rounded-full glow"
              />
              <span className="text-xl font-bold text-glow">
                {language === 'ja' ? 'スタジオ青澄' : 'Studio Aosumi'}
              </span>
            </div>
            <p className="text-gray-400 text-sm leading-relaxed">
              Creating magical anime experiences with cutting-edge 3D animation and heartfelt storytelling.
            </p>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="space-y-4"
          >
            <h4 className="text-lg font-semibold text-glow">Quick Links</h4>
            <div className="space-y-2">
              {[
                { key: 'home', href: '#home' },
                { key: 'about', href: '#about' },
                { key: 'projects', href: '#projects' },
                { key: 'contact', href: '#contact' }
              ].map((link) => (
                <motion.a
                  key={link.key}
                  href={link.href}
                  whileHover={{ x: 5, color: '#4ecdc4' }}
                  className="block text-gray-400 hover:text-cyan-400 transition-colors duration-300 text-sm"
                >
                  {t(`nav.${link.key}`)}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Social Media */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-4"
          >
            <h4 className="text-lg font-semibold text-glow">{t('footer.social')}</h4>
            <div className="flex space-x-4">
              {[
                { name: 'Twitter', icon: '𝕏' },
                { name: 'Instagram', icon: '📷' },
                { name: 'YouTube', icon: '📺' },
                { name: 'Discord', icon: '💬' }
              ].map((social, index) => (
                <motion.button
                  key={social.name}
                  whileHover={{ scale: 1.2, y: -3 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-white hover:glow transition-all duration-300"
                  title={social.name}
                >
                  <span className="text-sm">{social.icon}</span>
                </motion.button>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Divider */}
        <motion.div
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.3 }}
          className="h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent mb-8"
        />

        {/* Copyright */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center"
        >
          <p className="text-gray-400 text-sm">
            {t('footer.copyright')}
          </p>
          <motion.p
            className="text-xs text-gray-500 mt-2"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            Made with ❤️ for anime lovers worldwide
          </motion.p>
        </motion.div>
      </div>

      {/* Background Animation */}
      <motion.div
        className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-500"
        animate={{ 
          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
        }}
        transition={{ 
          duration: 5, 
          repeat: Infinity,
          ease: "linear"
        }}
        style={{ backgroundSize: '200% 200%' }}
      />

      {/* Floating Elements */}
      <motion.div
        className="absolute top-4 right-4 w-8 h-8 border border-cyan-400 rounded-full opacity-20"
        animate={{ rotate: 360 }}
        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
      />
      <motion.div
        className="absolute top-8 left-8 w-6 h-6 border border-pink-400 rounded-lg opacity-20"
        animate={{ rotate: -360 }}
        transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
      />
    </footer>
  );
}
