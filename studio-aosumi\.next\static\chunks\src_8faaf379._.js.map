{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/lib/translations.ts"], "sourcesContent": ["export const translations = {\n  en: {\n    nav: {\n      home: \"Home\",\n      about: \"About\",\n      projects: \"Projects\",\n      contact: \"Contact\",\n      language: \"Language\"\n    },\n    hero: {\n      title: \"Studio Aosumi\",\n      subtitle: \"Creating Magical Anime Experiences\",\n      description: \"We bring your anime dreams to life with cutting-edge 3D animation and storytelling that touches the heart.\",\n      cta: \"Explore Our World\"\n    },\n    about: {\n      title: \"About Studio Aosumi\",\n      description: \"Studio Aosumi is a premier anime production studio dedicated to creating immersive 3D animated experiences. Our team of passionate artists and storytellers work tirelessly to bring unique characters and worlds to life.\",\n      mission: \"Our Mission\",\n      missionText: \"To create anime that inspires, entertains, and connects people across cultures through the universal language of animation.\",\n      vision: \"Our Vision\",\n      visionText: \"To be the leading studio in innovative 3D anime production, setting new standards for visual storytelling.\"\n    },\n    projects: {\n      title: \"Our Projects\",\n      subtitle: \"Discover our latest anime creations\",\n      project1: {\n        title: \"Mystic Chronicles\",\n        description: \"An epic fantasy adventure following young heroes on their quest to save their world.\"\n      },\n      project2: {\n        title: \"Cyber Dreams\",\n        description: \"A futuristic tale of AI and humanity in a neon-lit cyberpunk world.\"\n      },\n      project3: {\n        title: \"Ocean's Heart\",\n        description: \"A heartwarming story about friendship and courage beneath the waves.\"\n      }\n    },\n    contact: {\n      title: \"Contact Us\",\n      subtitle: \"Let's create something amazing together\",\n      email: \"Email\",\n      phone: \"Phone\",\n      address: \"Address\",\n      form: {\n        name: \"Name\",\n        email: \"Email\",\n        message: \"Message\",\n        send: \"Send Message\"\n      }\n    },\n    footer: {\n      copyright: \"© 2024 Studio Aosumi. All rights reserved.\",\n      social: \"Follow Us\"\n    }\n  },\n  ja: {\n    nav: {\n      home: \"ホーム\",\n      about: \"私たちについて\",\n      projects: \"プロジェクト\",\n      contact: \"お問い合わせ\",\n      language: \"言語\"\n    },\n    hero: {\n      title: \"スタジオ青澄\",\n      subtitle: \"魔法のアニメ体験を創造\",\n      description: \"最先端の3Dアニメーションと心に響くストーリーテリングで、あなたのアニメの夢を現実にします。\",\n      cta: \"私たちの世界を探索\"\n    },\n    about: {\n      title: \"スタジオ青澄について\",\n      description: \"スタジオ青澄は、没入感のある3Dアニメーション体験の創造に専念するプレミアアニメ制作スタジオです。情熱的なアーティストとストーリーテラーのチームが、ユニークなキャラクターと世界を生き生きと描き出すために日夜努力しています。\",\n      mission: \"私たちの使命\",\n      missionText: \"アニメーションという普遍的な言語を通じて、文化を超えて人々を鼓舞し、楽しませ、つなぐアニメを創造すること。\",\n      vision: \"私たちのビジョン\",\n      visionText: \"革新的な3Dアニメ制作のリーディングスタジオとなり、ビジュアルストーリーテリングの新しい基準を設定すること。\"\n    },\n    projects: {\n      title: \"私たちのプロジェクト\",\n      subtitle: \"最新のアニメ作品をご覧ください\",\n      project1: {\n        title: \"ミスティック・クロニクルズ\",\n        description: \"世界を救うクエストに挑む若いヒーローたちの壮大なファンタジー冒険。\"\n      },\n      project2: {\n        title: \"サイバー・ドリームス\",\n        description: \"ネオンに照らされたサイバーパンクの世界でのAIと人類の未来的な物語。\"\n      },\n      project3: {\n        title: \"オーシャンズ・ハート\",\n        description: \"波の下での友情と勇気についての心温まる物語。\"\n      }\n    },\n    contact: {\n      title: \"お問い合わせ\",\n      subtitle: \"一緒に素晴らしいものを創造しましょう\",\n      email: \"メール\",\n      phone: \"電話\",\n      address: \"住所\",\n      form: {\n        name: \"お名前\",\n        email: \"メールアドレス\",\n        message: \"メッセージ\",\n        send: \"メッセージを送信\"\n      }\n    },\n    footer: {\n      copyright: \"© 2024 スタジオ青澄. 全著作権所有。\",\n      social: \"フォローする\"\n    }\n  }\n};\n\nexport type Language = 'en' | 'ja';\nexport type TranslationKey = keyof typeof translations.en;\n"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe;IAC1B,IAAI;QACF,KAAK;YACH,MAAM;YACN,OAAO;YACP,UAAU;YACV,SAAS;YACT,UAAU;QACZ;QACA,MAAM;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,KAAK;QACP;QACA,OAAO;YACL,OAAO;YACP,aAAa;YACb,SAAS;YACT,aAAa;YACb,QAAQ;YACR,YAAY;QACd;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,UAAU;gBACR,OAAO;gBACP,aAAa;YACf;YACA,UAAU;gBACR,OAAO;gBACP,aAAa;YACf;YACA,UAAU;gBACR,OAAO;gBACP,aAAa;YACf;QACF;QACA,SAAS;YACP,OAAO;YACP,UAAU;YACV,OAAO;YACP,OAAO;YACP,SAAS;YACT,MAAM;gBAC<PERSON>,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,WAAW;YACX,QAAQ;QACV;IACF;IACA,IAAI;QACF,KAAK;YACH,MAAM;YACN,OAAO;YACP,UAAU;YACV,SAAS;YACT,UAAU;QACZ;QACA,MAAM;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,KAAK;QACP;QACA,OAAO;YACL,OAAO;YACP,aAAa;YACb,SAAS;YACT,aAAa;YACb,QAAQ;YACR,YAAY;QACd;QACA,UAAU;YACR,OAAO;YACP,UAAU;YACV,UAAU;gBACR,OAAO;gBACP,aAAa;YACf;YACA,UAAU;gBACR,OAAO;gBACP,aAAa;YACf;YACA,UAAU;gBACR,OAAO;gBACP,aAAa;YACf;QACF;QACA,SAAS;YACP,OAAO;YACP,UAAU;YACV,OAAO;YACP,OAAO;YACP,SAAS;YACT,MAAM;gBACJ,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,WAAW;YACX,QAAQ;QACV;IACF;AACF", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { translations, Language } from '@/lib/translations';\n\ninterface LanguageContextType {\n  language: Language;\n  setLanguage: (lang: Language) => void;\n  t: (key: string) => string;\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\nexport const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [language, setLanguage] = useState<Language>('en');\n\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('language') as Language;\n    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ja')) {\n      setLanguage(savedLanguage);\n    }\n  }, []);\n\n  const handleSetLanguage = (lang: Language) => {\n    setLanguage(lang);\n    localStorage.setItem('language', lang);\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[language];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return (\n    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\nexport const useLanguage = () => {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAWA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,MAAM,mBAA4D;QAAC,EAAE,QAAQ,EAAE;;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,iBAAiB,CAAC,kBAAkB,QAAQ,kBAAkB,IAAI,GAAG;gBACvE,YAAY;YACd;QACF;qCAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,aAAa,OAAO,CAAC,YAAY;IACnC;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,6HAAA,CAAA,eAAY,CAAC,SAAS;QAEvC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,kBAAA,4BAAA,KAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU,aAAa;YAAmB;QAAE;kBAC5E;;;;;;AAGP;GA/Ba;KAAA;AAiCN,MAAM,cAAc;;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/components/ParticleBackground.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useMemo } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { Points, PointMaterial } from '@react-three/drei';\nimport * as THREE from 'three';\n\nfunction AnimatedParticles() {\n  const ref = useRef<THREE.Points>(null);\n  \n  const particlesPosition = useMemo(() => {\n    const positions = new Float32Array(5000 * 3);\n    \n    for (let i = 0; i < 5000; i++) {\n      positions[i * 3] = (Math.random() - 0.5) * 10;\n      positions[i * 3 + 1] = (Math.random() - 0.5) * 10;\n      positions[i * 3 + 2] = (Math.random() - 0.5) * 10;\n    }\n    \n    return positions;\n  }, []);\n\n  useFrame((state) => {\n    if (ref.current) {\n      ref.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.1;\n      ref.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.15) * 0.1;\n    }\n  });\n\n  return (\n    <Points ref={ref} positions={particlesPosition} stride={3} frustumCulled={false}>\n      <PointMaterial\n        transparent\n        color=\"#4ecdc4\"\n        size={0.005}\n        sizeAttenuation={true}\n        depthWrite={false}\n      />\n    </Points>\n  );\n}\n\nfunction FloatingCubes() {\n  const meshRef = useRef<THREE.Group>(null);\n  \n  const cubes = useMemo(() => {\n    const temp = [];\n    for (let i = 0; i < 50; i++) {\n      temp.push({\n        position: [\n          (Math.random() - 0.5) * 8,\n          (Math.random() - 0.5) * 8,\n          (Math.random() - 0.5) * 8\n        ],\n        rotation: [Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI],\n        scale: Math.random() * 0.1 + 0.05\n      });\n    }\n    return temp;\n  }, []);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.05) * 0.1;\n      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.08) * 0.1;\n    }\n  });\n\n  return (\n    <group ref={meshRef}>\n      {cubes.map((cube, index) => (\n        <mesh\n          key={index}\n          position={cube.position as [number, number, number]}\n          rotation={cube.rotation as [number, number, number]}\n          scale={cube.scale}\n        >\n          <boxGeometry args={[1, 1, 1]} />\n          <meshStandardMaterial\n            color=\"#45b7d1\"\n            transparent\n            opacity={0.3}\n            wireframe\n          />\n        </mesh>\n      ))}\n    </group>\n  );\n}\n\nexport default function ParticleBackground() {\n  return (\n    <div className=\"fixed inset-0 -z-10\">\n      <Canvas\n        camera={{ position: [0, 0, 5], fov: 75 }}\n        style={{ background: 'transparent' }}\n      >\n        <ambientLight intensity={0.5} />\n        <pointLight position={[10, 10, 10]} />\n        <AnimatedParticles />\n        <FloatingCubes />\n      </Canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;;;AAJA;;;;AAOA,SAAS;;IACP,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IAEjC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wDAAE;YAChC,MAAM,YAAY,IAAI,aAAa,OAAO;YAE1C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC3C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACjD;YAEA,OAAO;QACT;uDAAG,EAAE;IAEL,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;sCAAE,CAAC;YACR,IAAI,IAAI,OAAO,EAAE;gBACf,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;gBACnE,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ;YACtE;QACF;;IAEA,qBACE,6LAAC,6JAAA,CAAA,SAAM;QAAC,KAAK;QAAK,WAAW;QAAmB,QAAQ;QAAG,eAAe;kBACxE,cAAA,6LAAC,oKAAA,CAAA,gBAAa;YACZ,WAAW;YACX,OAAM;YACN,MAAM;YACN,iBAAiB;YACjB,YAAY;;;;;;;;;;;AAIpB;GAjCS;;QAeP,kNAAA,CAAA,WAAQ;;;KAfD;AAmCT,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IAEpC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCAAE;YACpB,MAAM,OAAO,EAAE;YACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;gBAC3B,KAAK,IAAI,CAAC;oBACR,UAAU;wBACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;qBACzB;oBACD,UAAU;wBAAC,KAAK,MAAM,KAAK,KAAK,EAAE;wBAAE,KAAK,MAAM,KAAK,KAAK,EAAE;wBAAE,KAAK,MAAM,KAAK,KAAK,EAAE;qBAAC;oBACrF,OAAO,KAAK,MAAM,KAAK,MAAM;gBAC/B;YACF;YACA,OAAO;QACT;uCAAG,EAAE;IAEL,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;kCAAE,CAAC;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ;gBACxE,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ;YAC1E;QACF;;IAEA,qBACE,6LAAC;QAAM,KAAK;kBACT,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gBAEC,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;;kCAEjB,6LAAC;wBAAY,MAAM;4BAAC;4BAAG;4BAAG;yBAAE;;;;;;kCAC5B,6LAAC;wBACC,OAAM;wBACN,WAAW;wBACX,SAAS;wBACT,SAAS;;;;;;;eAVN;;;;;;;;;;AAgBf;IA9CS;;QAmBP,kNAAA,CAAA,WAAQ;;;MAnBD;AAgDM,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,OAAO;gBAAE,YAAY;YAAc;;8BAEnC,6LAAC;oBAAa,WAAW;;;;;;8BACzB,6LAAC;oBAAW,UAAU;wBAAC;wBAAI;wBAAI;qBAAG;;;;;;8BAClC,6LAAC;;;;;8BACD,6LAAC;;;;;;;;;;;;;;;;AAIT;MAdwB", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Image from 'next/image';\n\nexport default function Header() {\n  const { language, setLanguage, t } = useLanguage();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navItems = [\n    { key: 'home', href: '#home' },\n    { key: 'about', href: '#about' },\n    { key: 'projects', href: '#projects' },\n    { key: 'contact', href: '#contact' }\n  ];\n\n  return (\n    <motion.header\n      initial={{ y: -100, opacity: 0 }}\n      animate={{ y: 0, opacity: 1 }}\n      transition={{ duration: 0.8 }}\n      className=\"fixed top-0 left-0 right-0 z-50 glass-effect\"\n    >\n      <nav className=\"container mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-3\"\n          >\n            <Image\n              src=\"/LOGO.jpg\"\n              alt=\"Studio Aosumi Logo\"\n              width={50}\n              height={50}\n              className=\"rounded-full glow\"\n            />\n            <span className=\"text-xl font-bold text-glow\">\n              {language === 'ja' ? 'スタジオ青澄' : 'Studio Aosumi'}\n            </span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <motion.a\n                key={item.key}\n                href={item.href}\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"text-white hover:text-cyan-400 transition-colors duration-300\"\n              >\n                {t(`nav.${item.key}`)}\n              </motion.a>\n            ))}\n            \n            {/* Language Switcher */}\n            <div className=\"flex items-center space-x-2\">\n              <motion.button\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setLanguage(language === 'en' ? 'ja' : 'en')}\n                className=\"px-3 py-1 rounded-full glass-effect text-sm font-medium hover:glow transition-all duration-300\"\n              >\n                {language === 'en' ? '日本語' : 'English'}\n              </motion.button>\n            </div>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            whileTap={{ scale: 0.95 }}\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden text-white\"\n          >\n            <svg\n              className=\"w-6 h-6\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </motion.button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"md:hidden mt-4 glass-effect rounded-lg p-4\"\n          >\n            {navItems.map((item) => (\n              <motion.a\n                key={item.key}\n                href={item.href}\n                whileHover={{ x: 10 }}\n                onClick={() => setIsMenuOpen(false)}\n                className=\"block py-2 text-white hover:text-cyan-400 transition-colors duration-300\"\n              >\n                {t(`nav.${item.key}`)}\n              </motion.a>\n            ))}\n            <motion.button\n              whileHover={{ x: 10 }}\n              onClick={() => {\n                setLanguage(language === 'en' ? 'ja' : 'en');\n                setIsMenuOpen(false);\n              }}\n              className=\"block py-2 text-white hover:text-cyan-400 transition-colors duration-300\"\n            >\n              {language === 'en' ? '日本語' : 'English'}\n            </motion.button>\n          </motion.div>\n        )}\n      </nav>\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW;QACf;YAAE,KAAK;YAAQ,MAAM;QAAQ;QAC7B;YAAE,KAAK;YAAS,MAAM;QAAS;QAC/B;YAAE,KAAK;YAAY,MAAM;QAAY;QACrC;YAAE,KAAK;YAAW,MAAM;QAAW;KACpC;IAED,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC/B,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,6LAAC;oCAAK,WAAU;8CACb,aAAa,OAAO,WAAW;;;;;;;;;;;;sCAKpC,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,KAAK,IAAI;wCACf,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDAET,EAAE,AAAC,OAAe,OAAT,KAAK,GAAG;uCANb,KAAK,GAAG;;;;;8CAWjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,YAAY,aAAa,OAAO,OAAO;wCACtD,WAAU;kDAET,aAAa,OAAO,QAAQ;;;;;;;;;;;;;;;;;sCAMnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAEV,cAAA,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAEP,2BACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAQX,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;wBAET,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCAEP,MAAM,KAAK,IAAI;gCACf,YAAY;oCAAE,GAAG;gCAAG;gCACpB,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CAET,EAAE,AAAC,OAAe,OAAT,KAAK,GAAG;+BANb,KAAK,GAAG;;;;;sCASjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,GAAG;4BAAG;4BACpB,SAAS;gCACP,YAAY,aAAa,OAAO,OAAO;gCACvC,cAAc;4BAChB;4BACA,WAAU;sCAET,aAAa,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GAjIwB;;QACe,sIAAA,CAAA,cAAW;;;KAD1B", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nexport default function HeroSection() {\n  const { t } = useLanguage();\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0\">\n        {[...Array(20)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-cyan-400 rounded-full opacity-60\"\n            animate={{\n              x: [0, Math.random() * 100 - 50],\n              y: [0, Math.random() * 100 - 50],\n              scale: [1, 1.5, 1],\n            }}\n            transition={{\n              duration: Math.random() * 3 + 2,\n              repeat: Infinity,\n              repeatType: \"reverse\",\n            }}\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"container mx-auto px-6 text-center relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, delay: 0.2 }}\n        >\n          {/* Main Title */}\n          <motion.h1\n            className=\"text-6xl md:text-8xl font-bold mb-6 anime-gradient bg-clip-text text-transparent\"\n            animate={{ \n              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n            }}\n            transition={{ \n              duration: 8, \n              repeat: Infinity,\n              ease: \"linear\"\n            }}\n          >\n            {t('hero.title')}\n          </motion.h1>\n\n          {/* Subtitle */}\n          <motion.h2\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.5 }}\n            className=\"text-2xl md:text-4xl font-light mb-8 text-glow\"\n          >\n            {t('hero.subtitle')}\n          </motion.h2>\n\n          {/* Description */}\n          <motion.p\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1, delay: 0.8 }}\n            className=\"text-lg md:text-xl max-w-3xl mx-auto mb-12 text-gray-300 leading-relaxed\"\n          >\n            {t('hero.description')}\n          </motion.p>\n\n          {/* CTA Button */}\n          <motion.button\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 1.1 }}\n            whileHover={{ \n              scale: 1.05,\n              boxShadow: \"0 0 30px rgba(78, 205, 196, 0.5)\"\n            }}\n            whileTap={{ scale: 0.95 }}\n            className=\"px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full text-white font-semibold text-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 glow\"\n            onClick={() => document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })}\n          >\n            {t('hero.cta')}\n          </motion.button>\n        </motion.div>\n\n        {/* Floating Elements */}\n        <motion.div\n          className=\"absolute top-20 left-10 w-20 h-20 border-2 border-cyan-400 rounded-full opacity-30\"\n          animate={{ \n            rotate: 360,\n            scale: [1, 1.2, 1]\n          }}\n          transition={{ \n            rotate: { duration: 20, repeat: Infinity, ease: \"linear\" },\n            scale: { duration: 4, repeat: Infinity }\n          }}\n        />\n\n        <motion.div\n          className=\"absolute bottom-20 right-10 w-16 h-16 border-2 border-pink-400 rounded-lg opacity-30\"\n          animate={{ \n            rotate: -360,\n            y: [0, -20, 0]\n          }}\n          transition={{ \n            rotate: { duration: 15, repeat: Infinity, ease: \"linear\" },\n            y: { duration: 3, repeat: Infinity }\n          }}\n        />\n\n        <motion.div\n          className=\"absolute top-1/2 right-20 w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-40\"\n          animate={{ \n            x: [0, 30, 0],\n            y: [0, -30, 0]\n          }}\n          transition={{ \n            duration: 6, \n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n        />\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 2 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-white rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAExB,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,KAAK,MAAM,KAAK,MAAM;6BAAG;4BAChC,GAAG;gCAAC;gCAAG,KAAK,MAAM,KAAK,MAAM;6BAAG;4BAChC,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU,KAAK,MAAM,KAAK,IAAI;4BAC9B,QAAQ;4BACR,YAAY;wBACd;wBACA,OAAO;4BACL,MAAM,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;4BAC7B,KAAK,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;wBAC9B;uBAfK;;;;;;;;;;0BAoBX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;;0CAGtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCACP,oBAAoB;wCAAC;wCAAU;wCAAY;qCAAS;gCACtD;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;0CAEC,EAAE;;;;;;0CAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;0CAET,EAAE;;;;;;0CAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;0CAET,EAAE;;;;;;0CAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,YAAY;oCACV,OAAO;oCACP,WAAW;gCACb;gCACA,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;gCACV,SAAS;wCAAM;4CAAA,2BAAA,SAAS,cAAc,CAAC,sBAAxB,+CAAA,yBAAkC,cAAc,CAAC;wCAAE,UAAU;oCAAS;;0CAEpF,EAAE;;;;;;;;;;;;kCAKP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;4BACR,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,QAAQ;gCAAE,UAAU;gCAAI,QAAQ;gCAAU,MAAM;4BAAS;4BACzD,OAAO;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;wBACzC;;;;;;kCAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ,CAAC;4BACT,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,QAAQ;gCAAE,UAAU;gCAAI,QAAQ;gCAAU,MAAM;4BAAS;4BACzD,GAAG;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;wBACrC;;;;;;kCAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAKJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAE;gBACvB,WAAU;0BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;GAnJwB;;QACR,sIAAA,CAAA,cAAW;;;KADH", "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/components/AboutSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nexport default function AboutSection() {\n  const { t } = useLanguage();\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.8 }\n    }\n  };\n\n  return (\n    <section id=\"about\" className=\"py-20 relative\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"max-w-6xl mx-auto\"\n        >\n          {/* Section Title */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-4xl md:text-6xl font-bold mb-6 text-glow\">\n              {t('about.title')}\n            </h2>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-cyan-400 to-blue-500 mx-auto rounded-full\" />\n          </motion.div>\n\n          {/* Main Description */}\n          <motion.div variants={itemVariants} className=\"mb-16\">\n            <div className=\"glass-effect rounded-2xl p-8 md:p-12\">\n              <p className=\"text-lg md:text-xl text-gray-300 leading-relaxed text-center\">\n                {t('about.description')}\n              </p>\n            </div>\n          </motion.div>\n\n          {/* Mission and Vision Cards */}\n          <div className=\"grid md:grid-cols-2 gap-8\">\n            {/* Mission Card */}\n            <motion.div\n              variants={itemVariants}\n              whileHover={{ scale: 1.02, y: -10 }}\n              className=\"glass-effect rounded-2xl p-8 card-hover\"\n            >\n              <div className=\"flex items-center mb-6\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center mr-4\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-2xl font-bold text-glow\">{t('about.mission')}</h3>\n              </div>\n              <p className=\"text-gray-300 leading-relaxed\">\n                {t('about.missionText')}\n              </p>\n            </motion.div>\n\n            {/* Vision Card */}\n            <motion.div\n              variants={itemVariants}\n              whileHover={{ scale: 1.02, y: -10 }}\n              className=\"glass-effect rounded-2xl p-8 card-hover\"\n            >\n              <div className=\"flex items-center mb-6\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center mr-4\">\n                  <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-2xl font-bold text-glow\">{t('about.vision')}</h3>\n              </div>\n              <p className=\"text-gray-300 leading-relaxed\">\n                {t('about.visionText')}\n              </p>\n            </motion.div>\n          </div>\n\n          {/* Stats Section */}\n          <motion.div\n            variants={itemVariants}\n            className=\"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8\"\n          >\n            {[\n              { number: '50+', label: 'Projects' },\n              { number: '100+', label: 'Characters' },\n              { number: '5+', label: 'Years' },\n              { number: '20+', label: 'Awards' }\n            ].map((stat, index) => (\n              <motion.div\n                key={index}\n                whileHover={{ scale: 1.1 }}\n                className=\"text-center glass-effect rounded-xl p-6\"\n              >\n                <div className=\"text-3xl md:text-4xl font-bold text-glow mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-gray-400 text-sm uppercase tracking-wider\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Background Decorations */}\n      <motion.div\n        className=\"absolute top-20 left-10 w-32 h-32 border border-cyan-400 rounded-full opacity-20\"\n        animate={{ rotate: 360 }}\n        transition={{ duration: 30, repeat: Infinity, ease: \"linear\" }}\n      />\n      <motion.div\n        className=\"absolute bottom-20 right-10 w-24 h-24 border border-pink-400 rounded-lg opacity-20\"\n        animate={{ rotate: -360 }}\n        transition={{ duration: 25, repeat: Infinity, ease: \"linear\" }}\n      />\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAExB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;;0BAC5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,6LAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;sCAC5C,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;;;;;sCAMT,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAU;oCACV,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAG;oCAClC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC5E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAG,WAAU;8DAAgC,EAAE;;;;;;;;;;;;sDAElD,6LAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;8CAKP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAU;oCACV,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAG;oCAClC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;;0EAC5E,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;0EACrE,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAG,WAAU;8DAAgC,EAAE;;;;;;;;;;;;sDAElD,6LAAC;4CAAE,WAAU;sDACV,EAAE;;;;;;;;;;;;;;;;;;sCAMT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET;gCACC;oCAAE,QAAQ;oCAAO,OAAO;gCAAW;gCACnC;oCAAE,QAAQ;oCAAQ,OAAO;gCAAa;gCACtC;oCAAE,QAAQ;oCAAM,OAAO;gCAAQ;gCAC/B;oCAAE,QAAQ;oCAAO,OAAO;gCAAS;6BAClC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCARR;;;;;;;;;;;;;;;;;;;;;0BAiBf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAI,QAAQ;oBAAU,MAAM;gBAAS;;;;;;0BAE/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ,CAAC;gBAAI;gBACxB,YAAY;oBAAE,UAAU;oBAAI,QAAQ;oBAAU,MAAM;gBAAS;;;;;;;;;;;;AAIrE;GApIwB;;QACR,sIAAA,CAAA,cAAW;;;KADH", "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/components/ProjectsSection.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nexport default function ProjectsSection() {\n  const { t } = useLanguage();\n\n  const projects = [\n    {\n      id: 1,\n      key: 'project1',\n      gradient: 'from-purple-500 to-pink-500',\n      icon: '⚔️'\n    },\n    {\n      id: 2,\n      key: 'project2',\n      gradient: 'from-cyan-500 to-blue-500',\n      icon: '🤖'\n    },\n    {\n      id: 3,\n      key: 'project3',\n      gradient: 'from-blue-500 to-teal-500',\n      icon: '🌊'\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, y: 50, scale: 0.9 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: { duration: 0.8 }\n    }\n  };\n\n  return (\n    <section id=\"projects\" className=\"py-20 relative\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold mb-6 text-glow\">\n            {t('projects.title')}\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            {t('projects.subtitle')}\n          </p>\n          <div className=\"w-24 h-1 bg-gradient-to-r from-cyan-400 to-blue-500 mx-auto rounded-full\" />\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.2 }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto\"\n        >\n          {projects.map((project) => (\n            <motion.div\n              key={project.id}\n              variants={cardVariants}\n              whileHover={{ \n                scale: 1.05, \n                y: -10,\n                rotateY: 5\n              }}\n              className=\"glass-effect rounded-2xl overflow-hidden card-hover group\"\n            >\n              {/* Project Header */}\n              <div className={`h-48 bg-gradient-to-br ${project.gradient} relative overflow-hidden`}>\n                <div className=\"absolute inset-0 bg-black bg-opacity-20\" />\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <motion.div\n                    whileHover={{ scale: 1.2, rotate: 10 }}\n                    className=\"text-6xl\"\n                  >\n                    {project.icon}\n                  </motion.div>\n                </div>\n                \n                {/* Animated Background Elements */}\n                <motion.div\n                  className=\"absolute top-4 right-4 w-8 h-8 border-2 border-white rounded-full opacity-30\"\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 10, repeat: Infinity, ease: \"linear\" }}\n                />\n                <motion.div\n                  className=\"absolute bottom-4 left-4 w-6 h-6 border-2 border-white rounded-lg opacity-30\"\n                  animate={{ rotate: -360 }}\n                  transition={{ duration: 8, repeat: Infinity, ease: \"linear\" }}\n                />\n              </div>\n\n              {/* Project Content */}\n              <div className=\"p-6\">\n                <motion.h3\n                  className=\"text-2xl font-bold mb-4 text-glow group-hover:text-cyan-400 transition-colors duration-300\"\n                >\n                  {t(`projects.${project.key}.title`)}\n                </motion.h3>\n                <p className=\"text-gray-300 leading-relaxed mb-6\">\n                  {t(`projects.${project.key}.description`)}\n                </p>\n                \n                {/* Action Button */}\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className={`w-full py-3 bg-gradient-to-r ${project.gradient} rounded-lg text-white font-semibold hover:shadow-lg transition-all duration-300`}\n                >\n                  View Project\n                </motion.button>\n              </div>\n\n              {/* Hover Effect Overlay */}\n              <motion.div\n                className=\"absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n              />\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Additional Projects Teaser */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          className=\"text-center mt-16\"\n        >\n          <motion.button\n            whileHover={{ \n              scale: 1.05,\n              boxShadow: \"0 0 30px rgba(78, 205, 196, 0.3)\"\n            }}\n            whileTap={{ scale: 0.95 }}\n            className=\"px-8 py-4 glass-effect rounded-full text-white font-semibold hover:glow transition-all duration-300\"\n          >\n            View All Projects\n          </motion.button>\n        </motion.div>\n      </div>\n\n      {/* Background Decorations */}\n      <motion.div\n        className=\"absolute top-1/4 left-0 w-64 h-64 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl\"\n        animate={{ \n          scale: [1, 1.2, 1],\n          opacity: [0.3, 0.6, 0.3]\n        }}\n        transition={{ \n          duration: 8, \n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }}\n      />\n      <motion.div\n        className=\"absolute bottom-1/4 right-0 w-64 h-64 bg-gradient-to-r from-cyan-400/10 to-blue-400/10 rounded-full blur-3xl\"\n        animate={{ \n          scale: [1.2, 1, 1.2],\n          opacity: [0.6, 0.3, 0.6]\n        }}\n        transition={{ \n          duration: 8, \n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }}\n      />\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAMe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAExB,MAAM,WAAW;QACf;YACE,IAAI;YACJ,KAAK;YACL,UAAU;YACV,MAAM;QACR;QACA;YACE,IAAI;YACJ,KAAK;YACL,UAAU;YACV,MAAM;QACR;QACA;YACE,IAAI;YACJ,KAAK;YACL,UAAU;YACV,MAAM;QACR;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAI;QACxC,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;;0BAC/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;4BAAM,QAAQ;wBAAI;wBACpC,WAAU;kCAET,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,YAAY;oCACV,OAAO;oCACP,GAAG,CAAC;oCACJ,SAAS;gCACX;gCACA,WAAU;;kDAGV,6LAAC;wCAAI,WAAW,AAAC,0BAA0C,OAAjB,QAAQ,QAAQ,EAAC;;0DACzD,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;wDAAK,QAAQ;oDAAG;oDACrC,WAAU;8DAET,QAAQ,IAAI;;;;;;;;;;;0DAKjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ;gDAAI;gDACvB,YAAY;oDAAE,UAAU;oDAAI,QAAQ;oDAAU,MAAM;gDAAS;;;;;;0DAE/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ,CAAC;gDAAI;gDACxB,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAS;;;;;;;;;;;;kDAKhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,WAAU;0DAET,EAAE,AAAC,YAAuB,OAAZ,QAAQ,GAAG,EAAC;;;;;;0DAE7B,6LAAC;gDAAE,WAAU;0DACV,EAAE,AAAC,YAAuB,OAAZ,QAAQ,GAAG,EAAC;;;;;;0DAI7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAW,AAAC,gCAAgD,OAAjB,QAAQ,QAAQ,EAAC;0DAC7D;;;;;;;;;;;;kDAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;;;;;;;+BAzDP,QAAQ,EAAE;;;;;;;;;;kCAgErB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCACV,OAAO;gCACP,WAAW;4BACb;4BACA,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAK;wBAAG;qBAAI;oBACpB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;;;;;;;AAIR;GAvLwB;;QACR,sIAAA,CAAA,cAAW;;;KADH", "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/components/ContactSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nexport default function ContactSection() {\n  const { t } = useLanguage();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    message: ''\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission here\n    console.log('Form submitted:', formData);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const contactInfo = [\n    {\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      ),\n      label: t('contact.email'),\n      value: '<EMAIL>'\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n        </svg>\n      ),\n      label: t('contact.phone'),\n      value: '+81 3-1234-5678'\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n      ),\n      label: t('contact.address'),\n      value: 'Tokyo, Japan'\n    }\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-20 relative\">\n      <div className=\"container mx-auto px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold mb-6 text-glow\">\n            {t('contact.title')}\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            {t('contact.subtitle')}\n          </p>\n          <div className=\"w-24 h-1 bg-gradient-to-r from-cyan-400 to-blue-500 mx-auto rounded-full\" />\n        </motion.div>\n\n        <div className=\"max-w-6xl mx-auto grid lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-8\"\n          >\n            <div className=\"glass-effect rounded-2xl p-8\">\n              <h3 className=\"text-2xl font-bold mb-8 text-glow\">Get in Touch</h3>\n              \n              {contactInfo.map((info, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  viewport={{ once: true }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  whileHover={{ x: 10 }}\n                  className=\"flex items-center space-x-4 p-4 rounded-lg hover:glass-effect transition-all duration-300 mb-4\"\n                >\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-white\">\n                    {info.icon}\n                  </div>\n                  <div>\n                    <div className=\"text-sm text-gray-400 uppercase tracking-wider\">\n                      {info.label}\n                    </div>\n                    <div className=\"text-white font-medium\">\n                      {info.value}\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Social Media */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.8, delay: 0.3 }}\n              className=\"glass-effect rounded-2xl p-8\"\n            >\n              <h4 className=\"text-xl font-bold mb-6 text-glow\">{t('footer.social')}</h4>\n              <div className=\"flex space-x-4\">\n                {['twitter', 'instagram', 'youtube', 'discord'].map((social, index) => (\n                  <motion.button\n                    key={social}\n                    whileHover={{ scale: 1.1, y: -5 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center text-white hover:glow transition-all duration-300\"\n                  >\n                    <span className=\"text-sm font-bold uppercase\">\n                      {social[0]}\n                    </span>\n                  </motion.button>\n                ))}\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8 }}\n            className=\"glass-effect rounded-2xl p-8\"\n          >\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  {t('contact.form.name')}\n                </label>\n                <motion.input\n                  whileFocus={{ scale: 1.02 }}\n                  type=\"text\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:glow transition-all duration-300\"\n                  placeholder={t('contact.form.name')}\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  {t('contact.form.email')}\n                </label>\n                <motion.input\n                  whileFocus={{ scale: 1.02 }}\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:glow transition-all duration-300\"\n                  placeholder={t('contact.form.email')}\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  {t('contact.form.message')}\n                </label>\n                <motion.textarea\n                  whileFocus={{ scale: 1.02 }}\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  rows={5}\n                  className=\"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:glow transition-all duration-300 resize-none\"\n                  placeholder={t('contact.form.message')}\n                  required\n                />\n              </div>\n\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                type=\"submit\"\n                className=\"w-full py-4 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-semibold hover:from-cyan-600 hover:to-blue-600 hover:glow transition-all duration-300\"\n              >\n                {t('contact.form.send')}\n              </motion.button>\n            </form>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Background Effects */}\n      <motion.div\n        className=\"absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-cyan-400/5 to-blue-400/5 rounded-full blur-3xl\"\n        animate={{ \n          scale: [1, 1.3, 1],\n          rotate: [0, 180, 360]\n        }}\n        transition={{ \n          duration: 20, \n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n      />\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,cAAc;QAClB;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO,EAAE;YACT,OAAO;QACT;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO,EAAE;YACT,OAAO;QACT;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCACjE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;kCACrE,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;YAGzE,OAAO,EAAE;YACT,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;;0BAC9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;4CAEjD,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,UAAU;wDAAE,MAAM;oDAAK;oDACvB,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,YAAY;wDAAE,GAAG;oDAAG;oDACpB,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI;;;;;;sEAEZ,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EACZ,KAAK,KAAK;;;;;;8EAEb,6LAAC;oEAAI,WAAU;8EACZ,KAAK,KAAK;;;;;;;;;;;;;mDAhBV;;;;;;;;;;;kDAwBX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,6LAAC;gDAAG,WAAU;0DAAoC,EAAE;;;;;;0DACpD,6LAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAW;oDAAa;oDAAW;iDAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC3D,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDAEZ,YAAY;4DAAE,OAAO;4DAAK,GAAG,CAAC;wDAAE;wDAChC,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;kEAEV,cAAA,6LAAC;4DAAK,WAAU;sEACb,MAAM,CAAC,EAAE;;;;;;uDANP;;;;;;;;;;;;;;;;;;;;;;0CAef,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,EAAE;;;;;;8DAEL,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;oDACX,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAU;oDACV,aAAa,EAAE;oDACf,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,EAAE;;;;;;8DAEL,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;oDACX,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAa,EAAE;oDACf,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DACd,EAAE;;;;;;8DAEL,6LAAC,6LAAA,CAAA,SAAM,CAAC,QAAQ;oDACd,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,MAAM;oDACN,WAAU;oDACV,aAAa,EAAE;oDACf,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,MAAK;4CACL,WAAU;sDAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,QAAQ;wBAAC;wBAAG;wBAAK;qBAAI;gBACvB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;;;;;;;AAIR;GA3NwB;;QACR,sIAAA,CAAA,cAAW;;;KADH", "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Image from 'next/image';\n\nexport default function Footer() {\n  const { t, language } = useLanguage();\n\n  return (\n    <footer className=\"relative py-12 glass-effect border-t border-white/10\">\n      <div className=\"container mx-auto px-6\">\n        <div className=\"grid md:grid-cols-3 gap-8 mb-8\">\n          {/* Logo and Description */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-4\"\n          >\n            <div className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/LOGO.jpg\"\n                alt=\"Studio Aosumi Logo\"\n                width={40}\n                height={40}\n                className=\"rounded-full glow\"\n              />\n              <span className=\"text-xl font-bold text-glow\">\n                {language === 'ja' ? 'スタジオ青澄' : 'Studio Aosumi'}\n              </span>\n            </div>\n            <p className=\"text-gray-400 text-sm leading-relaxed\">\n              Creating magical anime experiences with cutting-edge 3D animation and heartfelt storytelling.\n            </p>\n          </motion.div>\n\n          {/* Quick Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, delay: 0.1 }}\n            className=\"space-y-4\"\n          >\n            <h4 className=\"text-lg font-semibold text-glow\">Quick Links</h4>\n            <div className=\"space-y-2\">\n              {[\n                { key: 'home', href: '#home' },\n                { key: 'about', href: '#about' },\n                { key: 'projects', href: '#projects' },\n                { key: 'contact', href: '#contact' }\n              ].map((link) => (\n                <motion.a\n                  key={link.key}\n                  href={link.href}\n                  whileHover={{ x: 5, color: '#4ecdc4' }}\n                  className=\"block text-gray-400 hover:text-cyan-400 transition-colors duration-300 text-sm\"\n                >\n                  {t(`nav.${link.key}`)}\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Social Media */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            viewport={{ once: true }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"space-y-4\"\n          >\n            <h4 className=\"text-lg font-semibold text-glow\">{t('footer.social')}</h4>\n            <div className=\"flex space-x-4\">\n              {[\n                { name: 'Twitter', icon: '𝕏' },\n                { name: 'Instagram', icon: '📷' },\n                { name: 'YouTube', icon: '📺' },\n                { name: 'Discord', icon: '💬' }\n              ].map((social, index) => (\n                <motion.button\n                  key={social.name}\n                  whileHover={{ scale: 1.2, y: -3 }}\n                  whileTap={{ scale: 0.9 }}\n                  className=\"w-10 h-10 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-white hover:glow transition-all duration-300\"\n                  title={social.name}\n                >\n                  <span className=\"text-sm\">{social.icon}</span>\n                </motion.button>\n              ))}\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Divider */}\n        <motion.div\n          initial={{ scaleX: 0 }}\n          whileInView={{ scaleX: 1 }}\n          viewport={{ once: true }}\n          transition={{ duration: 1, delay: 0.3 }}\n          className=\"h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent mb-8\"\n        />\n\n        {/* Copyright */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"text-center\"\n        >\n          <p className=\"text-gray-400 text-sm\">\n            {t('footer.copyright')}\n          </p>\n          <motion.p\n            className=\"text-xs text-gray-500 mt-2\"\n            animate={{ opacity: [0.5, 1, 0.5] }}\n            transition={{ duration: 3, repeat: Infinity }}\n          >\n            Made with ❤️ for anime lovers worldwide\n          </motion.p>\n        </motion.div>\n      </div>\n\n      {/* Background Animation */}\n      <motion.div\n        className=\"absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-500\"\n        animate={{ \n          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n        }}\n        transition={{ \n          duration: 5, \n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n        style={{ backgroundSize: '200% 200%' }}\n      />\n\n      {/* Floating Elements */}\n      <motion.div\n        className=\"absolute top-4 right-4 w-8 h-8 border border-cyan-400 rounded-full opacity-20\"\n        animate={{ rotate: 360 }}\n        transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n      />\n      <motion.div\n        className=\"absolute top-8 left-8 w-6 h-6 border border-pink-400 rounded-lg opacity-20\"\n        animate={{ rotate: -360 }}\n        transition={{ duration: 15, repeat: Infinity, ease: \"linear\" }}\n      />\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAElC,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DACb,aAAa,OAAO,WAAW;;;;;;;;;;;;kDAGpC,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAMvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,KAAK;gDAAQ,MAAM;4CAAQ;4CAC7B;gDAAE,KAAK;gDAAS,MAAM;4CAAS;4CAC/B;gDAAE,KAAK;gDAAY,MAAM;4CAAY;4CACrC;gDAAE,KAAK;gDAAW,MAAM;4CAAW;yCACpC,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,KAAK,IAAI;gDACf,YAAY;oDAAE,GAAG;oDAAG,OAAO;gDAAU;gDACrC,WAAU;0DAET,EAAE,AAAC,OAAe,OAAT,KAAK,GAAG;+CALb,KAAK,GAAG;;;;;;;;;;;;;;;;0CAYrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,6LAAC;wCAAG,WAAU;kDAAmC,EAAE;;;;;;kDACnD,6LAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,MAAM;gDAAW,MAAM;4CAAK;4CAC9B;gDAAE,MAAM;gDAAa,MAAM;4CAAK;4CAChC;gDAAE,MAAM;gDAAW,MAAM;4CAAK;4CAC9B;gDAAE,MAAM;gDAAW,MAAM;4CAAK;yCAC/B,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,YAAY;oDAAE,OAAO;oDAAK,GAAG,CAAC;gDAAE;gDAChC,UAAU;oDAAE,OAAO;gDAAI;gDACvB,WAAU;gDACV,OAAO,OAAO,IAAI;0DAElB,cAAA,6LAAC;oDAAK,WAAU;8DAAW,OAAO,IAAI;;;;;;+CANjC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAc1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ;wBAAE;wBACrB,aAAa;4BAAE,QAAQ;wBAAE;wBACzB,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;wBACtC,WAAU;;;;;;kCAIZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCAAC;gCAClC,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;0CAC7C;;;;;;;;;;;;;;;;;;0BAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,oBAAoB;wBAAC;wBAAU;wBAAY;qBAAS;gBACtD;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,OAAO;oBAAE,gBAAgB;gBAAY;;;;;;0BAIvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAI,QAAQ;oBAAU,MAAM;gBAAS;;;;;;0BAE/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ,CAAC;gBAAI;gBACxB,YAAY;oBAAE,UAAU;oBAAI,QAAQ;oBAAU,MAAM;gBAAS;;;;;;;;;;;;AAIrE;GAnJwB;;QACE,sIAAA,CAAA,cAAW;;;KADb", "debugId": null}}, {"offset": {"line": 2678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/454/studio-aosumi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { LanguageProvider } from '@/contexts/LanguageContext';\nimport ParticleBackground from '@/components/ParticleBackground';\nimport Header from '@/components/Header';\nimport HeroSection from '@/components/HeroSection';\nimport AboutSection from '@/components/AboutSection';\nimport ProjectsSection from '@/components/ProjectsSection';\nimport ContactSection from '@/components/ContactSection';\nimport Footer from '@/components/Footer';\n\nexport default function Home() {\n  return (\n    <LanguageProvider>\n      <div className=\"relative\">\n        <ParticleBackground />\n        <Header />\n        <main>\n          <HeroSection />\n          <AboutSection />\n          <ProjectsSection />\n          <ContactSection />\n        </main>\n        <Footer />\n      </div>\n    </LanguageProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,qBACE,6LAAC,sIAAA,CAAA,mBAAgB;kBACf,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,2IAAA,CAAA,UAAkB;;;;;8BACnB,6LAAC,+HAAA,CAAA,UAAM;;;;;8BACP,6LAAC;;sCACC,6LAAC,oIAAA,CAAA,UAAW;;;;;sCACZ,6LAAC,qIAAA,CAAA,UAAY;;;;;sCACb,6LAAC,wIAAA,CAAA,UAAe;;;;;sCAChB,6LAAC,uIAAA,CAAA,UAAc;;;;;;;;;;;8BAEjB,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf;KAhBwB", "debugId": null}}]}